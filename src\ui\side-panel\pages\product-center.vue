<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useShopBinding } from '../../../composables/useShopBinding'
import { useNotification } from '../../../composables/useNotification'
import dianxiaomiDetectionService from '../../../services/dianxiaomiDetectionService'
import type { ShopAccount, FreightTemplate, ProductCategory } from '../../../services/dianxiaomiDetectionService'
import BasicSettings from '../components/BasicSettings.vue'
import ProductConfig from '../components/ProductConfig.vue'
import CollectionConfig from '../components/CollectionConfig.vue'
import { getSiteOptions } from '../../../config/temuSites'

// 使用组合式函数
const { shopBinding } = useShopBinding()
const { success, error } = useNotification()

// 步骤定义
const steps = [
  {
    title: '基础设置',
    description: '配置ERP平台、店铺信息等基础设置'
  },
  {
    title: '上品配置',
    description: '配置库存、价格、标题等上品参数'
  },
  {
    title: '货盘采集',
    description: '配置货盘采集规则和数据源'
  }
]

// 当前步骤
const activeStep = ref(0)

// 切换步骤
const switchStep = (step: number) => {
  activeStep.value = step
}

// 店小秘登录状态
const dianxiaomiLoginStatus = ref({
  isLoggedIn: false,
  message: '请先登录店小秘ERP系统',
  loading: false
})

// 数据状态
const shopAccounts = ref<ShopAccount[]>([])
const warehouses = ref<Record<string, Record<string, Record<string, string>>>>({})
const freightTemplates = ref<FreightTemplate[]>([])
const productCategories = ref<ProductCategory[]>([])

// 加载状态
const loadingStates = ref({
  shopAccounts: false,
  warehouses: false,
  freightTemplates: false,
  productCategories: false
})

// 基础设置表单
const basicForm = ref({
  erpPlatform: '店小秘',
  publishSite: '',
  shopAccount: '',
  publishStatus: '2',
  businessSite: '',
  warehouse: '',
  freightTemplate: '',
  shippingTime: '86400',
  venue: '',
  productCategory: '',
  productAttributes: ''
})

// 上品配置表单
const configForm = ref({
  minStock: 12,
  fixedStock: null,
  enableDeduplication: true,
  titlePrefix: '',
  titleSuffix: '',
  uploadInterval: 0,
  priceMultiplier: 4,
  collectDetails: ['title', 'img'],
  collectSku: true,
  externalLink: false,
  defaultSize: {
    length: 10,
    width: 10,
    height: 10,
    weight: 20
  },
  filterProhibited: true,
  prohibitedWords: '',
  enableTranslation: false,
  translationService: 'google'
})

// 货盘采集表单
const collectionForm = ref({
  dataSource: 'temu',
  collectionUrl: '',
  autoCollection: false,
  collectionInterval: 60,
  maxItems: 100,
  filterRules: '',
  keywords: '',
  priceRange: {
    min: null,
    max: null
  },
  categoryFilter: '',
  ratingFilter: 4.0,
  salesFilter: 0,
  enableProxy: false,
  proxyConfig: '',
  collectImages: true,
  collectReviews: false,
  maxReviews: 50
})

// 计算属性
const currentTemuShop = computed(() => {
  if (shopBinding.temuSiteInfo) {
    return `${shopBinding.temuSiteInfo.mallName} (${shopBinding.temuSiteInfo.isSemiManagedMall ? '半托' : '全托'})`
  }
  return 'Temu'
})

// 仓库选项
const getWarehouseOptions = computed(() => {
  const options: Array<{ value: string; label: string; shopId: string; site: string }> = []

  Object.entries(warehouses.value).forEach(([shopId, shopWarehouses]) => {
    Object.entries(shopWarehouses).forEach(([site, siteWarehouses]) => {
      Object.entries(siteWarehouses).forEach(([warehouseId, warehouseName]) => {
        options.push({
          value: warehouseId,
          label: `${warehouseName} (店铺: ${shopId}, 站点: ${site})`,
          shopId,
          site
        })
      })
    })
  })

  return options
})

// 检查授权是否即将过期（30天内）
const isExpiringSoon = (expireTime: string): boolean => {
  if (!expireTime) return false
  try {
    const expireDate = new Date(expireTime)
    const now = new Date()
    const diffTime = expireDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays <= 30 && diffDays > 0
  } catch {
    return false
  }
}

// 方法
const checkDianxiaomiLogin = async () => {
  dianxiaomiLoginStatus.value.loading = true
  try {
    const result = await dianxiaomiDetectionService.checkLoginStatus()
    dianxiaomiLoginStatus.value = {
      isLoggedIn: result.isLoggedIn,
      message: result.message,
      loading: false
    }

    if (result.isLoggedIn) {
      // 如果已登录，自动获取数据
      await Promise.all([
        loadShopAccounts(),
        loadWarehouses(),
        loadFreightTemplates(),
        loadProductCategories()
      ])
    }
  } catch (err) {
    console.error('[ProductCenter] 检测店小秘登录失败:', err)
    dianxiaomiLoginStatus.value = {
      isLoggedIn: false,
      message: '检测失败，请重试',
      loading: false
    }
  }
}

const openDianxiaomiERP = () => {
  window.open('https://www.dianxiaomi.com', '_blank')
}

const loadShopAccounts = async () => {
  console.info('[ProductCenter] 开始加载店铺账号...')
  loadingStates.value.shopAccounts = true
  try {
    console.info('[ProductCenter] 调用 dianxiaomiDetectionService.getShopAccounts()')
    const result = await dianxiaomiDetectionService.getShopAccounts()
    console.info('[ProductCenter] 店铺账号API返回结果:', result)

    if (result.success && result.data) {
      shopAccounts.value = result.data
      console.info('[ProductCenter] 成功设置店铺账号数据:', result.data)

      // 优先使用session中保存的选中店铺，否则选择第一个
      const selectedShopId = await dianxiaomiDetectionService.getSelectedShopId()
      if (selectedShopId && result.data.some(shop => shop.shopId === selectedShopId)) {
        basicForm.value.shopAccount = selectedShopId
        console.info('[ProductCenter] 使用session中的选中店铺:', selectedShopId)
      } else if (result.data.length > 0 && !basicForm.value.shopAccount) {
        basicForm.value.shopAccount = result.data[0].shopId
        console.info('[ProductCenter] 默认选择第一个店铺:', result.data[0])
        // 保存到session
        await dianxiaomiDetectionService.setSelectedShopId(result.data[0].shopId)
      }

      // 显示成功提示
      success('店铺账号同步成功', `成功获取 ${result.data.length} 个店铺账号`)
    } else {
      console.error('[ProductCenter] 获取店铺账号失败:', result.error)
      error('获取店铺账号失败', result.error || '未知错误')
    }
  } catch (err) {
    console.error('[ProductCenter] 加载店铺账号异常:', err)
    error('加载店铺账号失败', err instanceof Error ? err.message : '未知错误')
  } finally {
    loadingStates.value.shopAccounts = false
    console.info('[ProductCenter] 店铺账号加载完成')
  }
}

const loadWarehouses = async () => {
  loadingStates.value.warehouses = true
  try {
    const result = await dianxiaomiDetectionService.getWarehouses()
    if (result.success && result.data) {
      warehouses.value = result.data

      // 计算仓库总数
      let warehouseCount = 0
      Object.values(result.data).forEach(shopWarehouses => {
        Object.values(shopWarehouses).forEach(siteWarehouses => {
          warehouseCount += Object.keys(siteWarehouses).length
        })
      })

      // 显示成功提示
      success('发货仓库同步成功', `成功获取 ${warehouseCount} 个发货仓库`)
    } else {
      error('获取发货仓库失败', result.error || '未知错误')
    }
  } catch (err) {
    console.error('[ProductCenter] 加载发货仓库失败:', err)
    error('加载发货仓库失败', err instanceof Error ? err.message : '未知错误')
  } finally {
    loadingStates.value.warehouses = false
  }
}

const loadFreightTemplates = async () => {
  loadingStates.value.freightTemplates = true
  try {
    const result = await dianxiaomiDetectionService.getFreightTemplates()
    if (result.success && result.data) {
      freightTemplates.value = result.data
      // 如果有运费模板，默认选择第一个
      if (result.data.length > 0 && !basicForm.value.freightTemplate) {
        basicForm.value.freightTemplate = result.data[0].freightTemplateId
      }

      // 显示成功提示
      success('运费模板同步成功', `成功获取 ${result.data.length} 个运费模板`)
    } else {
      error('获取运费模板失败', result.error || '未知错误')
    }
  } catch (err) {
    console.error('[ProductCenter] 加载运费模板失败:', err)
    error('加载运费模板失败', err instanceof Error ? err.message : '未知错误')
  } finally {
    loadingStates.value.freightTemplates = false
  }
}

const loadProductCategories = async () => {
  loadingStates.value.productCategories = true
  try {
    const result = await dianxiaomiDetectionService.getProductCategories()
    if (result.success && result.data) {
      productCategories.value = result.data
    } else {
      error('获取商品分类失败', result.error || '未知错误')
    }
  } catch (err) {
    console.error('[ProductCenter] 加载商品分类失败:', err)
    error('加载商品分类失败', err instanceof Error ? err.message : '未知错误')
  } finally {
    loadingStates.value.productCategories = false
  }
}

const onShopAccountChange = async (shopId: string) => {
  if (shopId) {
    console.info('[ProductCenter] 店铺选择变化:', shopId)
    await dianxiaomiDetectionService.setSelectedShopId(shopId)

    // 重新加载依赖店铺的数据
    await Promise.all([
      loadWarehouses(),
      loadFreightTemplates()
    ])
  }
}

const onCategoryChange = (category: ProductCategory | null) => {
  console.info('[ProductCenter] 分类变更:', category)
  if (category) {
    basicForm.value.productCategory = category.catName
  }
}

const loadCategoriesByParent = async (parentId?: number): Promise<ProductCategory[]> => {
  try {
    return await dianxiaomiDetectionService.getCategoriesByParent(parentId)
  } catch (err) {
    console.error('[ProductCenter] 加载子分类失败:', err)
    return []
  }
}

// 保存方法
const saveBasicSettings = () => {
  console.info('保存基础设置:', basicForm.value)
  success('基础设置保存成功！')
}

const saveConfig = () => {
  console.info('保存上品配置:', configForm.value)
  success('上品配置保存成功！')
}

const startCollection = () => {
  console.info('开始货盘采集:', collectionForm.value)
  success('货盘采集已启动！')
}

// 生命周期
onMounted(() => {
  checkDianxiaomiLogin()
})
</script>

<template>
  <div class="h-full flex flex-col bg-gray-50">
    <!-- 步骤导航 -->
    <a-card class="mb-6 shadow-sm" :bordered="false">
      <template #title>
        <div class="flex items-center space-x-2">
          <span class="text-xl">🏭</span>
          <span class="text-lg font-semibold">产品中心配置</span>
        </div>
      </template>

      <a-steps
        :current="activeStep"
        class="mb-4"
        :items="steps"
        @change="switchStep"
      />
    </a-card>

    <!-- 主内容区域 -->
    <div class="flex-1 overflow-hidden">
      <div class="h-full overflow-y-auto">
        <!-- 基础设置 -->
        <BasicSettings
          v-if="activeStep === 0"
          v-model:basic-form="basicForm"
          :dianxiaomi-login-status="dianxiaomiLoginStatus"
          :shop-accounts="shopAccounts"
          :warehouses="warehouses"
          :freight-templates="freightTemplates"
          :product-categories="productCategories"
          :loading-states="loadingStates"
          :current-temu-shop="currentTemuShop"
          @check-login="checkDianxiaomiLogin"
          @open-erp="openDianxiaomiERP"
          @load-shop-accounts="loadShopAccounts"
          @load-warehouses="loadWarehouses"
          @load-freight-templates="loadFreightTemplates"
          @load-product-categories="loadProductCategories"
          @shop-account-change="onShopAccountChange"
          @category-change="onCategoryChange"
          @load-categories-by-parent="loadCategoriesByParent"
          @save-settings="saveBasicSettings"
        />

        <!-- 上品配置 -->
        <ProductConfig
          v-else-if="activeStep === 1"
          v-model:config-form="configForm"
          @save-config="saveConfig"
        />

        <!-- 货盘采集 -->
        <CollectionConfig
          v-else-if="activeStep === 2"
          v-model:collection-form="collectionForm"
          @start-collection="startCollection"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.ant-steps-item-title) {
  font-weight: 600;
}

:deep(.ant-steps-item-description) {
  color: #6b7280;
}
</style>