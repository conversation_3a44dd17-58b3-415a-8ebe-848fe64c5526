<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

// Props
const props = defineProps<{
  configForm: {
    minStock: number
    fixedStock: number | null
    enableDeduplication: boolean
    titlePrefix: string
    titleSuffix: string
    uploadInterval: number
    priceMultiplier: number
    collectDetails: string[]
    collectSku: boolean
    externalLink: boolean
    defaultSize: {
      length: number
      width: number
      height: number
      weight: number
    }
    filterProhibited: boolean
    prohibitedWords: string
    enableTranslation: boolean
    translationService: string
  }
}>()

// Emits
const emit = defineEmits<{
  'update:configForm': [value: typeof props.configForm]
  'save-config': []
}>()

// 更新表单数据
const updateForm = (key: string, value: any) => {
  const newForm = { ...props.configForm, [key]: value }
  emit('update:configForm', newForm)
}

// 更新嵌套对象
const updateNestedForm = (parentKey: string, childKey: string, value: any) => {
  const newForm = {
    ...props.configForm,
    [parentKey]: {
      ...props.configForm[parentKey as keyof typeof props.configForm],
      [childKey]: value
    }
  }
  emit('update:configForm', newForm)
}

const handleSave = () => {
  emit('save-config')
}
</script>

<template>
  <div class="product-config">
    <a-card 
      title="上品配置" 
      class="shadow-sm"
      :bordered="false"
    >
      <template #extra>
        <a-tag color="green">⚙️ 配置库存、价格、标题等上品参数</a-tag>
      </template>

      <a-form
        :model="configForm"
        layout="vertical"
        @finish="handleSave"
        class="space-y-6"
      >
        <!-- 库存设置 -->
        <a-card size="small" title="📦 库存设置" class="mb-6">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item 
                label="最小库存"
                name="minStock"
                :rules="[{ required: true, message: '请输入最小库存' }]"
              >
                <a-input-number
                  :value="configForm.minStock"
                  @change="(value) => updateForm('minStock', value)"
                  :min="0"
                  placeholder="请输入最小库存"
                  style="width: 100%"
                >
                  <template #addonAfter>件</template>
                </a-input-number>
                <div class="mt-1 text-xs text-gray-500">
                  货盘库存低于配置最小库存，将不发布上品
                </div>
              </a-form-item>
            </a-col>
            
            <a-col :span="12">
              <a-form-item label="固定上品库存">
                <a-input-number
                  :value="configForm.fixedStock"
                  @change="(value) => updateForm('fixedStock', value)"
                  :min="0"
                  placeholder="不设置则为空"
                  style="width: 100%"
                >
                  <template #addonAfter>件</template>
                </a-input-number>
                <div class="mt-1 text-xs text-gray-500">
                  <ul class="list-disc list-inside space-y-1">
                    <li>设置固定后，将以设置的库存数为上品库存</li>
                    <li>注意：更改保存后，请刷新货盘列表，否则数据不会生效</li>
                  </ul>
                </div>
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 去重设置 -->
          <a-form-item label="去重设置">
            <a-switch
              :checked="configForm.enableDeduplication"
              @change="(checked) => updateForm('enableDeduplication', checked)"
              checked-children="启用"
              un-checked-children="关闭"
            />
            <div class="mt-2 text-xs text-gray-500">
              <ul class="list-disc list-inside space-y-1">
                <li>如果启用，则上品将使用货盘的去重规则</li>
                <li>去重规则为：同个店铺同个货盘,如上次已上过品，则本次将不执行上品</li>
                <li>注意：更改保存后，请刷新货盘列表，否则数据不会生效</li>
              </ul>
            </div>
          </a-form-item>
        </a-card>

        <!-- 标题设置 -->
        <a-card size="small" title="📝 标题设置" class="mb-6">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="标题前缀">
                <a-input
                  :value="configForm.titlePrefix"
                  @change="(e) => updateForm('titlePrefix', e.target.value)"
                  :maxlength="100"
                  placeholder="请输入关键词(选填)"
                  show-count
                />
              </a-form-item>
            </a-col>
            
            <a-col :span="12">
              <a-form-item label="标题后缀">
                <a-input
                  :value="configForm.titleSuffix"
                  @change="(e) => updateForm('titleSuffix', e.target.value)"
                  :maxlength="170"
                  placeholder="请输入关键词(选填)"
                  show-count
                />
              </a-form-item>
            </a-col>
          </a-row>
          <div class="text-xs text-gray-500">
            选填，标题前缀和标题后缀，将分别添加到标题前面和后面
          </div>
        </a-card>

        <!-- 价格和时间设置 -->
        <a-card size="small" title="💰 价格和时间设置" class="mb-6">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item 
                label="上品价格上浮"
                name="priceMultiplier"
                :rules="[{ required: true, message: '请输入价格倍数' }]"
              >
                <a-input-number
                  :value="configForm.priceMultiplier"
                  @change="(value) => updateForm('priceMultiplier', value)"
                  :min="1"
                  :step="0.1"
                  placeholder="请输入倍数"
                  style="width: 100%"
                >
                  <template #addonBefore>默认</template>
                  <template #addonAfter>倍</template>
                </a-input-number>
              </a-form-item>
            </a-col>
            
            <a-col :span="12">
              <a-form-item label="上品时间间隔">
                <a-input-number
                  :value="configForm.uploadInterval"
                  @change="(value) => updateForm('uploadInterval', value)"
                  :min="0"
                  placeholder="默认为0"
                  style="width: 100%"
                >
                  <template #addonAfter>秒</template>
                </a-input-number>
                <div class="mt-1 text-xs text-gray-500">
                  <ul class="list-disc list-inside space-y-1">
                    <li>一般默认为0</li>
                    <li>注释：上完一个品后，下个品的时间间隔</li>
                  </ul>
                </div>
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>

        <!-- 采集设置 -->
        <a-card size="small" title="🌐 采集设置" class="mb-6">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="采集详情需要">
                <a-checkbox-group
                  :value="configForm.collectDetails"
                  @change="(value) => updateForm('collectDetails', value)"
                >
                  <a-checkbox value="title">文字</a-checkbox>
                  <a-checkbox value="img">详情图</a-checkbox>
                </a-checkbox-group>
              </a-form-item>
            </a-col>
            
            <a-col :span="12">
              <a-form-item label="其他采集选项">
                <a-space direction="vertical">
                  <a-switch
                    :checked="configForm.collectSku"
                    @change="(checked) => updateForm('collectSku', checked)"
                    checked-children="采集SKU货号"
                    un-checked-children="不采集SKU货号"
                  />
                  <a-switch
                    :checked="configForm.externalLink"
                    @change="(checked) => updateForm('externalLink', checked)"
                    checked-children="站外产品链接"
                    un-checked-children="不使用站外链接"
                  />
                </a-space>
                <div class="mt-2 text-xs text-gray-500">
                  <ul class="list-disc list-inside space-y-1">
                    <li>采集SKU货号：上品时自动把商品采集的货号填入SKU货号中</li>
                    <li>站外产品链接：自动把商品采集的链接填入站外产品链接（仅对TEMU采集模式有效）</li>
                  </ul>
                </div>
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>

        <!-- 默认尺寸设置 -->
        <a-card size="small" title="📏 默认尺寸设置" class="mb-6">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item label="长边">
                <a-input-number
                  :value="configForm.defaultSize.length"
                  @change="(value) => updateNestedForm('defaultSize', 'length', value)"
                  :min="0"
                  placeholder="长边"
                  style="width: 100%"
                >
                  <template #addonAfter>cm</template>
                </a-input-number>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="次长">
                <a-input-number
                  :value="configForm.defaultSize.width"
                  @change="(value) => updateNestedForm('defaultSize', 'width', value)"
                  :min="0"
                  placeholder="次长"
                  style="width: 100%"
                >
                  <template #addonAfter>cm</template>
                </a-input-number>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="短边">
                <a-input-number
                  :value="configForm.defaultSize.height"
                  @change="(value) => updateNestedForm('defaultSize', 'height', value)"
                  :min="0"
                  placeholder="短边"
                  style="width: 100%"
                >
                  <template #addonAfter>cm</template>
                </a-input-number>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="重量">
                <a-input-number
                  :value="configForm.defaultSize.weight"
                  @change="(value) => updateNestedForm('defaultSize', 'weight', value)"
                  :min="0"
                  placeholder="重量"
                  style="width: 100%"
                >
                  <template #addonAfter>g</template>
                </a-input-number>
              </a-form-item>
            </a-col>
          </a-row>
          <div class="text-xs text-gray-500">
            如果商品尺寸为空时，将会使用默认尺寸
          </div>
        </a-card>

        <!-- 违禁词过滤 -->
        <a-card size="small" title="🚫 违禁词过滤" class="mb-6">
          <a-form-item label="违禁词过滤">
            <a-switch
              :checked="configForm.filterProhibited"
              @change="(checked) => updateForm('filterProhibited', checked)"
              checked-children="启用"
              un-checked-children="关闭"
            />
          </a-form-item>

          <a-form-item v-if="configForm.filterProhibited" label="违禁词列表">
            <a-textarea
              :value="configForm.prohibitedWords"
              @change="(e) => updateForm('prohibitedWords', e.target.value)"
              :rows="4"
              placeholder="请输入违禁词，每行一个..."
            />
            <div class="mt-1 text-xs text-gray-500">
              <ul class="list-disc list-inside space-y-1">
                <li>启用后，系统将自动过滤包含违禁词的商品标题和描述</li>
                <li>每行输入一个违禁词，支持模糊匹配</li>
              </ul>
            </div>
          </a-form-item>
        </a-card>

        <!-- 翻译设置 -->
        <a-card size="small" title="🌍 翻译设置" class="mb-6">
          <a-form-item label="自动翻译">
            <a-switch
              :checked="configForm.enableTranslation"
              @change="(checked) => updateForm('enableTranslation', checked)"
              checked-children="启用"
              un-checked-children="关闭"
            />
          </a-form-item>

          <a-form-item v-if="configForm.enableTranslation" label="翻译服务">
            <a-select
              :value="configForm.translationService"
              @change="(value) => updateForm('translationService', value)"
              placeholder="请选择翻译服务"
              style="width: 200px"
            >
              <a-select-option value="google">Google 翻译</a-select-option>
              <a-select-option value="baidu">百度翻译</a-select-option>
              <a-select-option value="youdao">有道翻译</a-select-option>
            </a-select>
            <div class="mt-1 text-xs text-gray-500">
              <ul class="list-disc list-inside space-y-1">
                <li>启用后，系统将自动翻译商品标题和描述到目标语言</li>
                <li>翻译质量取决于所选择的翻译服务</li>
              </ul>
            </div>
          </a-form-item>
        </a-card>

        <!-- 保存按钮 -->
        <a-form-item>
          <div class="flex justify-center">
            <a-button
              type="primary"
              html-type="submit"
              size="large"
              class="px-8"
            >
              ⚙️ 保存上品配置
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<style scoped>
.product-config :deep(.ant-form-item-label > label) {
  font-weight: 600;
}

.product-config :deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 600;
}

.product-config :deep(.ant-card-small > .ant-card-head) {
  min-height: 48px;
}
</style>
